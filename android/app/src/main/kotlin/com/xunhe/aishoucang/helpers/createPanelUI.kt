package com.xunhe.aishoucang.helpers

import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.*
import android.view.animation.AnimationUtils
import android.widget.Button
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.helpers.CreateFavoriteHelper
import com.xunhe.aishoucang.lib.SharedPreferencesHelper
import com.xunhe.aishoucang.views.share_panel.SharePanelItem
import com.xunhe.aishoucang.views.share_panel.SharePanelManager
import com.xunhe.aishoucang.helpers.TaskQueueManager
import com.xunhe.aishoucang.models.TaskQueueItem
import com.xunhe.aishoucang.lib.ClipboardHelper
import com.xunhe.aishoucang.lib.FFmpegHelper
import com.xunhe.aishoucang.helpers.CustomToastHelper
import com.xunhe.aishoucang.helpers.UserAgentGenerator
import com.xunhe.aishoucang.helpers.WebViewHtmlExtractor

/**
 * 创建标准分享面板UI
 * 从SharePanelHelper中提取出来，方便复用
 */
object PanelUICreator {
    private const val TAG = "PanelUICreator"

    /**
     * 创建标准分享面板UI
     *
     * @param context 上下文
     * @param windowManager 窗口管理器
     * @param sharePanelView 分享面板视图引用
     * @param sharePanelParams 分享面板布局参数引用
     * @param hideSharePanel 隐藏分享面板的方法
     * @param unSupport 是否为不支持的应用
     * @param onComplete 完成回调
     */
    fun createPanelUI(
        context: Context,
        windowManager: WindowManager?,
        sharePanelView: View?,
        sharePanelParams: WindowManager.LayoutParams?,
        hideSharePanel: (Context, WindowManager?, Boolean) -> Unit,
        unSupport: Boolean = false,
        onComplete: () -> Unit = {}
    ): Pair<View?, WindowManager.LayoutParams?> {
        var updatedView = sharePanelView
        var updatedParams = sharePanelParams

        if (updatedView != null) {
            Log.d(TAG, "面板已存在，直接执行回调")
            onComplete()
            return Pair(updatedView, updatedParams)
        }

        Log.d(TAG, "开始创建标准面板UI")
        try {
            val screenHeight = context.resources.displayMetrics.heightPixels
            val minHeight = (screenHeight * 0.75).toInt()

            val inflater =
                context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            updatedView = inflater.inflate(R.layout.share_panel_layout, null)
            Log.d(TAG, "面板视图已创建")

            updatedView?.let { view ->
                // 设置整体最小高度（LinearLayout）
                view.minimumHeight = minHeight

                // 获取加载视图和内容视图
                val loadingView = view.findViewById<LinearLayout>(R.id.loading_container)
                val contentView = view.findViewById<LinearLayout>(R.id.share_panel_content)

                // 直接显示内容视图，不显示加载状态（使用缓存机制）
                loadingView?.visibility = View.GONE
                contentView?.visibility = View.VISIBLE

                // 设置标题文本
                val titleTextView = view.findViewById<TextView>(R.id.title_text_view)
                if (unSupport) {
                    titleTextView?.text = "新建收藏"
                    Log.d(TAG, "设置面板标题为：新建收藏")
                } else {
                    titleTextView?.text = "选择指定收藏夹"
                    Log.d(TAG, "设置面板标题为：选择指定收藏夹")
                }

                // 初始化RecyclerView
                val sharePanelManager = SharePanelManager(context)
                sharePanelManager.setupRecyclerView(view) { shareItem: SharePanelItem ->
                    // 仅在未处理中状态才响应点击
                    if (!SharePanelHelper.isProcessing) {
                        // 记录当前选中的分享项，然后先隐藏面板
                        SharePanelHelper.currentShareItem = shareItem
                        hideSharePanel(context, windowManager, true)
                    } else {
                        Log.d(TAG, "内容处理中，忽略点击事件")
                        Toast.makeText(context, "正在获取分享内容，请稍候...", Toast.LENGTH_SHORT).show()
                    }
                }

                // 设置新建收藏夹按钮
                val newFolderButton = view.findViewById<Button>(R.id.new_folder_button)

                // 检查用户是否已登录
                val userId = SharedPreferencesHelper.getInstance(context).getUserId()
                val isLoggedIn = userId.isNotEmpty()

                // 根据登录状态启用或禁用按钮
                newFolderButton?.isEnabled = isLoggedIn

                newFolderButton?.setOnClickListener {
                    // 仅在未处理中状态才响应点击
                    if (!SharePanelHelper.isProcessing) {
                        Log.d(TAG, "点击了新建收藏夹按钮")

                        // 再次检查用户是否已登录
                        val currentUserId = SharedPreferencesHelper.getInstance(context).getUserId()
                        if (currentUserId.isEmpty()) {
                            Toast.makeText(context, "请先登录", Toast.LENGTH_SHORT).show()
                            return@setOnClickListener
                        }

                        // 显示创建收藏夹对话框
                        CreateFavoriteHelper.showCreateFavoriteDialog(context) {
                            // 创建成功后刷新收藏夹列表
                            sharePanelManager.refreshRecyclerView(view)
                        }
                    } else {
                        Log.d(TAG, "内容处理中，忽略新建按钮点击")
                        Toast.makeText(context, "正在获取分享内容，请稍候...", Toast.LENGTH_SHORT).show()
                    }
                }

                // 初始化任务进度组件
                TaskProgressHelper.initProgressComponents(view)

                // 设置AI工具箱按钮点击事件
                setupAIToolsClickListeners(view, context)

                // 初始化笔记面板管理器
                setupNotePanelManager(view, context)

                // 初始化任务队列管理器
                val taskQueueManager = TaskQueueManager(context)
                taskQueueManager.setupTaskQueueRecyclerView(view) { taskItem: TaskQueueItem ->
                    // 处理任务项点击事件
                    Toast.makeText(context, "点击了任务: ${taskItem.title}", Toast.LENGTH_SHORT).show()
                }
                taskQueueManager.setupTabSwitching(view)

                // 设置关闭按钮
                view.findViewById<ImageButton>(R.id.close_button)?.setOnClickListener {
                    Log.d(TAG, "点击了关闭按钮")
                    SharePanelHelper.hideSharePanelWithoutParam(false)
                }

                // 设置取消按钮
                view.findViewById<Button>(R.id.cancel_button)?.setOnClickListener {
                    hideSharePanel(context, windowManager, false)
                }
            }

            updatedParams = WindowManager.LayoutParams().apply {
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                else WindowManager.LayoutParams.TYPE_PHONE
                format = PixelFormat.TRANSLUCENT
                flags =
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                            WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS or
                            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                width = WindowManager.LayoutParams.MATCH_PARENT
                height = minHeight
                gravity = Gravity.BOTTOM or Gravity.START
                dimAmount = 0f
                windowAnimations = R.style.SharePanelAnimation
            }

            Log.d(TAG, "添加面板到窗口")
            windowManager?.addView(updatedView, updatedParams)
            SharePanelHelper._isSharePanelShowing = true

            // 视图添加后开始动画
            updatedView?.post {
                Log.d(TAG, "视图已添加，准备开始动画")
                try {
                    val animation = AnimationUtils.loadAnimation(context, R.anim.slide_up)
                    updatedView?.startAnimation(animation)
                } catch (e: Exception) {
                    Log.e(TAG, "启动动画失败", e)
                }

                onComplete()
            }

        } catch (e: Exception) {
            Log.e(TAG, "显示分享面板时出错", e)
            // 确保在主线程中执行回调
            Handler(Looper.getMainLooper()).post {
                Log.d(TAG, "面板创建出错，执行回调")
                onComplete() // 出错时也调用回调以避免阻塞
            }
        }

        return Pair(updatedView, updatedParams)
    }

    /**
     * 设置AI工具箱按钮点击事件
     */
    private fun setupAIToolsClickListeners(view: View, context: Context) {
        // 创建笔记
        view.findViewById<LinearLayout>(R.id.tool_create_note)?.setOnClickListener {
            ClipboardLinkExtractor.handleCreateNoteClick(context, "创建笔记")
        }



        // 下载视频
        view.findViewById<LinearLayout>(R.id.tool_download_video)?.setOnClickListener {
            handleDownloadVideoClick(context)
        }

        // 快速二创
        // view.findViewById<LinearLayout>(R.id.tool_quick_create)?.setOnClickListener {
        //     Toast.makeText(context, "快速二创功能开发中...", Toast.LENGTH_SHORT).show()
        // }
    }

    /**
     * 处理下载视频按钮点击事件
     */
    private fun handleDownloadVideoClick(context: Context) {
        // 防连点检查
        if (SharePanelHelper.isProcessing) {
            Log.d(TAG, "正在处理中，忽略下载视频点击")
            Toast.makeText(context, "正在处理中，请稍候...", Toast.LENGTH_SHORT).show()
            return
        }

        try {
            // 获取剪贴板内容
            val clipboardHelper = ClipboardHelper.getInstance(context)
            val clipText = clipboardHelper.getClipboardText()

            if (clipText.isNullOrEmpty()) {
                CustomToastHelper.showToast(context, "剪贴板内容为空")
                return
            }

            // 提取链接
            val extractedLinks = extractLinks(clipText)
            if (extractedLinks.isEmpty()) {
                CustomToastHelper.showToast(context, "剪贴板中未找到有效链接")
                return
            }

            // 设置处理状态，防止重复点击
            SharePanelHelper.isProcessing = true

            SharePanelHelper.hideSharePanelWithoutParam()

            // 显示开始下载提示
            CustomToastHelper.showToast(context, "开始提取视频...")

            // 使用第一个链接进行视频提取
            val firstUrl = extractedLinks.first()
            extractVideoFromUrlForDownload(context, firstUrl)

        } catch (e: Exception) {
            // 重置处理状态
            SharePanelHelper.isProcessing = false
            Log.e(TAG, "下载视频时发生异常", e)
            CustomToastHelper.showToast(context, "下载视频时发生异常: ${e.message}")
        }
    }

    /**
     * 从URL提取视频资源用于下载
     * 参考extractVideoFromUrlForTask的实现
     */
    private fun extractVideoFromUrlForDownload(context: Context, url: String) {
        Log.d(TAG, "开始从URL提取视频资源用于下载: $url")

        // 使用WebViewHtmlExtractor执行extractVideo.js脚本
        WebViewHtmlExtractor.executeBusinessJavaScript(
            context = context,
            url = url,
            businessName = "extractVideo"
        ) { result, error ->
            if (error != null) {
                Log.e(TAG, "视频提取失败 - URL: $url, 错误: $error")
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    SharePanelHelper.isProcessing = false
                    CustomToastHelper.showToast(context, "视频提取失败: $error")
                }
                return@executeBusinessJavaScript
            } else if (result != null) {
                Log.d(TAG, "视频提取成功，结果: $result")

                try {
                    // 解析JSON结果
                    val jsonResult = org.json.JSONObject(result)
                    val success = jsonResult.optBoolean("success", false)

                    if (success) {
                        val videosArray = jsonResult.optJSONArray("videos")
                        if (videosArray != null && videosArray.length() > 0) {
                            // 获取第一个视频的URL
                            val firstVideo = videosArray.getJSONObject(0)
                            val videoSrc = firstVideo.optString("src", "")
                            val videoCurrentSrc = firstVideo.optString("currentSrc", "")

                            Log.d(TAG, "解析视频信息 - src: $videoSrc, currentSrc: $videoCurrentSrc")

                            // 优先使用currentSrc，如果没有或为null则使用src
                            val videoUrl = when {
                                videoCurrentSrc.isNotEmpty() && videoCurrentSrc != "null" -> videoCurrentSrc
                                videoSrc.isNotEmpty() && videoSrc != "null" -> videoSrc
                                else -> ""
                            }

                            if (videoUrl.isNotEmpty()) {
                                Log.d(TAG, "使用提取的视频URL进行下载: $videoUrl")
                                downloadVideoToGallery(context, videoUrl)
                            } else {
                                android.os.Handler(android.os.Looper.getMainLooper()).post {
                                    SharePanelHelper.isProcessing = false
                                    CustomToastHelper.showToast(context, "未能成功提取视频链接，请稍后再试")
                                }
                            }
                        } else {
                            android.os.Handler(android.os.Looper.getMainLooper()).post {
                                SharePanelHelper.isProcessing = false
                                CustomToastHelper.showToast(context, "未能成功提取视频链接，请稍后再试")
                            }
                        }
                    } else {
                        android.os.Handler(android.os.Looper.getMainLooper()).post {
                            SharePanelHelper.isProcessing = false
                            CustomToastHelper.showToast(context, "未能成功提取视频链接，请稍后再试")
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "解析视频提取结果时出错", e)
                    android.os.Handler(android.os.Looper.getMainLooper()).post {
                        SharePanelHelper.isProcessing = false
                        CustomToastHelper.showToast(context, "解析视频出错，请稍后再试")
                    }
                }
            } else {
                Log.d(TAG, "未从URL提取到任何视频资源，提示用户失败")
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    SharePanelHelper.isProcessing = false
                    CustomToastHelper.showToast(context, "剪切板内容解析异常，请稍后再试")
                }
            }
        }
    }

    /**
     * 下载视频到相册
     */
    private fun downloadVideoToGallery(context: Context, videoUrl: String) {
        Log.d(TAG, "开始下载视频到相册: $videoUrl")

        android.os.Handler(android.os.Looper.getMainLooper()).post {
            CustomToastHelper.showToast(context, "开始下载视频...")
        }

        val ffmpegHelper = FFmpegHelper.getInstance(context)
        val finalUserAgent = UserAgentGenerator.generateUserAgentForUrl(videoUrl)
        val finalReferer = UserAgentGenerator.getRefererForUrl(videoUrl)

        ffmpegHelper.downloadVideo(
            url = videoUrl,
            userAgent = finalUserAgent,
            referer = finalReferer,
            outputPath = null, // 使用默认路径
            saveToGallery = true // 保存到相册
        ) { success, outputFilePath, errorMessage ->
            android.os.Handler(android.os.Looper.getMainLooper()).post {
                // 重置处理状态
                SharePanelHelper.isProcessing = false

                if (success) {
                    CustomToastHelper.showToast(context, "视频下载成功，已保存到相册")
                    Log.d(TAG, "视频下载成功: $outputFilePath")
                } else {
                    CustomToastHelper.showToast(context, "视频下载失败: $errorMessage")
                    Log.e(TAG, "视频下载失败: $errorMessage")
                }
            }
        }
    }

    /**
     * 从文本中提取HTTP/HTTPS链接
     */
    private fun extractLinks(text: String): List<String> {
        val links = mutableListOf<String>()
        try {
            // 使用正则表达式匹配HTTP/HTTPS链接
            val urlPattern = Regex("https?://[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=%]+")
            val matches = urlPattern.findAll(text)

            for (match in matches) {
                val link = match.value
                if (!links.contains(link)) {
                    links.add(link)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "提取链接时发生错误", e)
        }
        return links
    }

    /**
     * 初始化笔记面板管理器
     */
    private fun setupNotePanelManager(view: View, context: Context) {
        val notePanelManager = com.xunhe.aishoucang.views.share_panel.NotePanelManager(context)
        notePanelManager.setupNotePanel(view)
    }
}
