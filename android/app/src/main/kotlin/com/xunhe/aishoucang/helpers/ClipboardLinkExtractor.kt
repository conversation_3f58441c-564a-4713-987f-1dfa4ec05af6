package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import android.widget.Toast
import com.xunhe.aishoucang.lib.ClipboardHelper
import com.xunhe.aishoucang.api.TaskApi
import com.xunhe.aishoucang.helpers.WebViewHtmlExtractor
import com.xunhe.aishoucang.helpers.SharePanelHelper
import com.xunhe.aishoucang.helpers.ContentTypeConstants
import com.xunhe.aishoucang.helpers.TaskStatusPoller
import com.xunhe.aishoucang.lib.FFmpegHelper
import com.xunhe.aishoucang.api.NoteApi
import com.xunhe.aishoucang.lib.RequestHelper
import com.xunhe.aishoucang.lib.SharedPreferencesHelper
import com.xunhe.aishoucang.helpers.ConfigHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.util.regex.Pattern

/**
 * 剪切板链接提取工具类
 * 负责从剪切板内容中提取HTTP/HTTPS链接，并提供防抖功能
 */
object ClipboardLinkExtractor {
    private const val TAG = "ClipboardLinkExtractor"

    // 防抖相关
    private var lastClickTime = 0L
    private const val DEBOUNCE_INTERVAL = 1000L // 1秒防抖间隔

    // HTTP/HTTPS链接匹配正则表达式 - 更精确的URL匹配，避免匹配到后续的中文内容
    private val URL_PATTERN = Pattern.compile(
        "https?://[^\\s，。！？；：、\\u4e00-\\u9fff]+",
        Pattern.CASE_INSENSITIVE
    )

    /**
     * 将ContentTypeConstants中的内容类型转换为简化的source_type
     * @param contentType ContentTypeConstants中定义的内容类型
     * @return 1=音视频内容，2=图文内容
     */
    private fun convertContentTypeToSourceType(contentType: Int): Int {
        return when (contentType) {
            // 音视频内容类型
            ContentTypeConstants.DOUYIN_TYPE_SHORT_VIDEO,
            ContentTypeConstants.DOUYIN_TYPE_LONG_VIDEO,
            ContentTypeConstants.DOUYIN_TYPE_LIVE,
            ContentTypeConstants.KUAISHOU_TYPE_SHORT_VIDEO,
            ContentTypeConstants.KUAISHOU_TYPE_LIVE,
            ContentTypeConstants.XIAOHONGSHU_TYPE_VIDEO_NOTE,
            ContentTypeConstants.BILIBILI_TYPE_VIDEO,
            ContentTypeConstants.BILIBILI_TYPE_LIVE,
            ContentTypeConstants.PINDUODUO_TYPE_LIVE,
            ContentTypeConstants.JINGDONG_TYPE_LIVE -> 1 // 音视频内容

            // 图文内容类型
            ContentTypeConstants.DOUYIN_TYPE_IMAGE_TEXT,
            ContentTypeConstants.KUAISHOU_TYPE_IMAGE_TEXT,
            ContentTypeConstants.XIAOHONGSHU_TYPE_NOTE,
            ContentTypeConstants.BILIBILI_TYPE_ARTICLE,
            ContentTypeConstants.WECHAT_TYPE_ARTICLE,
            ContentTypeConstants.DOUBAN_TYPE_NOTE,
            ContentTypeConstants.DOUBAN_TYPE_MOVIE,
            ContentTypeConstants.DOUBAN_TYPE_BOOK,
            ContentTypeConstants.DOUBAN_TYPE_MUSIC,
            ContentTypeConstants.MEITUAN_TYPE_RESTAURANT,
            ContentTypeConstants.MEITUAN_TYPE_HOTEL,
            ContentTypeConstants.PINDUODUO_TYPE_PRODUCT,
            ContentTypeConstants.TAOBAO_TYPE_PRODUCT,
            ContentTypeConstants.JINGDONG_TYPE_PRODUCT -> 2 // 图文内容

            // 默认情况（未知类型或其他）
            else -> 2 // 默认为图文内容
        }
    }

    /**
     * 处理创建笔记按钮点击事件
     * 包含防抖功能，并根据当前内容类型智能判断任务类型
     * 先关闭收藏面板，然后执行后续逻辑
     *
     * @param context 上下文
     * @param taskTitle 任务标题，如"创建笔记"
     */
    fun handleCreateNoteClick(context: Context, taskTitle: String = "创建笔记") {
        Log.d(TAG, "创建笔记按钮被点击")

        // 防抖检查
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastClickTime < DEBOUNCE_INTERVAL) {
            Log.d(TAG, "点击过于频繁，忽略本次操作")
            return
        }
        lastClickTime = currentTime

        // 先关闭收藏面板
        Log.d(TAG, "关闭收藏面板")
        SharePanelHelper.hideSharePanelWithoutParam(false)

        // 获取当前内容类型
        val currentContentType = SharePanelHelper.getCurrentContentType()
        Log.d(TAG, "当前内容类型: $currentContentType")

        // 将内容类型转换为简化的source_type
        val sourceType = convertContentTypeToSourceType(currentContentType)
        Log.d(TAG, "转换后的源类型: $sourceType (${if (sourceType == 1) "音视频内容" else "图文内容"})")

        // 先检查会员状态
        checkMembershipBeforeProcess(context, sourceType, taskTitle)
    }

    /**
     * 处理从剪切板创建任务的逻辑
     * 1. 获取剪切板内容
     * 2. 提取URL
     * 3. 提取视频链接
     * 4. 调用请求创建task
     *
     * @param context 上下文
     * @param sourceType 内容源类型：1=音视频内容，2=图文内容
     * @param taskTitle 任务标题
     */
    private fun processCreateTaskFromClipboard(context: Context, sourceType: Int, taskTitle: String) {
        try {
            Log.d(TAG, "开始处理剪切板内容创建任务，源类型: $sourceType")

            // 1. 获取剪切板内容
            val clipboardHelper = ClipboardHelper.getInstance(context)
            val clipboardContent = clipboardHelper.getClipboardText()

            Log.d(TAG, "获取到剪切板内容: $clipboardContent")

            if (clipboardContent.isNullOrEmpty()) {
                Log.w(TAG, "剪切板内容为空")
                Toast.makeText(context, "剪切板内容为空", Toast.LENGTH_SHORT).show()
                return
            }

            // 2. 从剪切板内容中提取URL
            val extractedLinks = extractLinks(clipboardContent)

            if (extractedLinks.isEmpty()) {
                Log.d(TAG, "未从剪切板内容中找到HTTP/HTTPS链接")
                Toast.makeText(context, "剪切板中未找到有效链接", Toast.LENGTH_SHORT).show()
                return
            }

            Log.d(TAG, "成功提取到 ${extractedLinks.size} 个链接:")
            extractedLinks.forEachIndexed { index, link ->
                Log.d(TAG, "链接 ${index + 1}: $link")
            }

            // 3. 校验链接是否为支持的平台（抖音或小红书）
            val firstUrl = extractedLinks.first()
            if (!isSupportedPlatform(firstUrl)) {
                Log.w(TAG, "不支持的平台链接: $firstUrl")
                Toast.makeText(context, "暂时只支持抖音和小红书链接", Toast.LENGTH_SHORT).show()
                return
            }

            // 显示输入框让用户输入笔记标题
            CustomInputModalHelper.showInputModal(
                context = context,
                title = "创建笔记",
                hint = "请输入笔记标题",
                cancelText = "取消",
                confirmText = "确认",
                callback = object : CustomInputModalHelper.InputModalCallback {
                    override fun onConfirm(inputText: String) {
                        Log.d(TAG, "用户输入笔记标题: $inputText")

                        // 1. 检查标题不能为空
                        if (inputText.trim().isEmpty()) {
                            CustomToastHelper.showToast(context, "笔记标题不能为空")
                            return
                        }

                        // 2. 保存标题到NoteApi的静态属性
                        NoteApi.currentNoteTitle = inputText.trim()
                        Log.d(TAG, "已保存笔记标题到NoteApi: ${NoteApi.currentNoteTitle}")

                        // 3. 继续后续逻辑
                        processUrlForTask(context, firstUrl, sourceType, inputText.trim())
                    }

                    override fun onCancel() {
                        Log.d(TAG, "用户取消输入笔记标题")
                        CustomToastHelper.showToast(context, "已取消创建笔记")
                    }
                }
            )

        } catch (e: Exception) {
            Log.e(TAG, "处理剪切板内容创建任务时发生错误", e)
            Toast.makeText(context, "处理失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 检查会员状态后再处理
     *
     * @param context 上下文
     * @param sourceType 内容源类型：1=音视频内容，2=图文内容
     * @param taskTitle 任务标题
     */
    private fun checkMembershipBeforeProcess(context: Context, sourceType: Int, taskTitle: String) {
        Log.d(TAG, "开始检查会员状态")

        // 检查用户是否已登录
        val currentUserId = SharedPreferencesHelper.getInstance(context).getUserId()
        if (currentUserId.isEmpty()) {
            Log.w(TAG, "用户未登录")
            CustomToastHelper.showToast(context, "请先登录")
            return
        }

        // 调用AI使用量API检查会员状态
        checkAiUsage(context) { success, hasUsage, error ->
            android.os.Handler(android.os.Looper.getMainLooper()).post {
                if (success) {
                    if (hasUsage) {
                        Log.d(TAG, "会员校验通过，继续检查笔记创建任务")
                        // 会员校验通过，继续检查笔记创建任务
                        checkCreateNoteTaskBeforeProcess(context, sourceType, taskTitle)
                    } else {
                        Log.w(TAG, "用户没有剩余使用次数")
                        CustomToastHelper.showToast(context, "您的AI使用次数已用完，请升级会员")
                    }
                } else {
                    Log.e(TAG, "检查会员状态失败: $error")
                    CustomToastHelper.showToast(context, "$error")
                }
            }
        }
    }

    /**
     * 检查AI使用量
     *
     * @param context 上下文
     * @param callback 回调函数，参数为(是否成功, 是否有剩余次数, 错误信息)
     */
    private fun checkAiUsage(context: Context, callback: (Boolean, Boolean, String?) -> Unit) {
        try {
            val requestHelper = RequestHelper.getInstance(context)
            val apiBaseUrl = ConfigHelper.getString("api_base_url")
            val url = "$apiBaseUrl/ai-usage/get"

            Log.d(TAG, "请求AI使用量API: $url")

            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val result = requestHelper.get(url)

                    when (result) {
                        is RequestHelper.ApiResult.Success -> {
                            val jsonResult = JSONObject(result.data)
                            val code = jsonResult.optInt("code", -1)

                            if (code == 0) {
                                val data = jsonResult.optJSONObject("data")
                                val usageCount = data?.optInt("usage_count", 0) ?: 0

                                Log.d(TAG, "AI使用量查询成功，剩余次数: $usageCount")
                                callback(true, usageCount > 0, null)
                            } else {
                                val message = jsonResult.optString("message", "未知错误")
                                Log.e(TAG, "AI使用量查询失败: $message")
                                callback(false, false, message)
                            }
                        }
                        is RequestHelper.ApiResult.Error -> {
                            // 尝试解析错误响应体中的具体错误信息
                            val errorMessage = try {
                                if (!result.errorBody.isNullOrEmpty()) {
                                    val errorJson = JSONObject(result.errorBody)
                                    val errorCode = errorJson.optInt("code", -1)
                                    val errorMsg = errorJson.optString("message", "未知错误")

                                    Log.e(TAG, "AI使用量API返回错误: code=$errorCode, message=$errorMsg")

                                    // 根据错误码返回相应的用户友好提示
                                    when (errorCode) {
                                        408 -> "无权使用AI功能，请联系管理员"
                                        401 -> "登录已过期，请重新登录"
                                        403 -> "权限不足，请联系管理员"
                                        else -> errorMsg
                                    }
                                } else {
                                    "网络请求失败，请稍后重试"
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "解析错误响应失败", e)
                                "网络请求失败，请稍后重试"
                            }

                            Log.e(TAG, "AI使用量API请求失败: $errorMessage")
                            callback(false, false, errorMessage)
                        }
                        is RequestHelper.ApiResult.Exception -> {
                            Log.e(TAG, "AI使用量API请求异常", result.throwable)
                            callback(false, false, result.throwable.message)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "检查AI使用量异常", e)
                    callback(false, false, e.message)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查AI使用量失败", e)
            callback(false, false, e.message)
        }
    }

    /**
     * 检查笔记创建任务后再处理
     *
     * @param context 上下文
     * @param sourceType 内容源类型：1=音视频内容，2=图文内容
     * @param taskTitle 任务标题
     */
    private fun checkCreateNoteTaskBeforeProcess(context: Context, sourceType: Int, taskTitle: String) {
        Log.d(TAG, "开始检查是否有正在进行的笔记创建任务")

        val taskApi = TaskApi.getInstance(context)
        taskApi.checkCreateNoteTask { success, hasTask, taskId, error ->
            // 在主线程处理结果
            android.os.Handler(android.os.Looper.getMainLooper()).post {
                if (success) {
                    if (hasTask) {
                        Log.d(TAG, "检测到正在进行的笔记创建任务: $taskId")
                        CustomToastHelper.showToast(context, "请先完成当前笔记创建，无法同时创建多个笔记")
                    } else {
                        Log.d(TAG, "没有正在进行的笔记创建任务，继续处理")
                        // 没有正在进行的任务，继续执行创建任务逻辑
                        processCreateTaskFromClipboard(context, sourceType, taskTitle)
                    }
                } else {
                    Log.e(TAG, "检查笔记创建任务失败: $error")
                    // 检查失败，为了用户体验，仍然继续执行创建任务逻辑
                    CustomToastHelper.showToast(context, "系统异常")
                }
            }
        }
    }

    /**
     * 处理URL创建任务
     * 新逻辑：先用webviewExtractHtml调用extractVideo获取视频链接，然后用最终的视频链接创建任务
     *
     * @param context 上下文
     * @param url 要处理的URL
     * @param sourceType 内容源类型：1=音视频内容，2=图文内容
     * @param taskTitle 任务标题
     */
    private fun processUrlForTask(context: Context, url: String, sourceType: Int, taskTitle: String) {
        Log.d(TAG, "开始处理URL创建任务: $url, 源类型: $sourceType")

        // 提取平台信息
        val platform = UrlPlatformExtractor.extractPlatformFromUrl(url)
        Log.d(TAG, "识别的平台: $platform")

        // 提取标题
        val title = UrlPlatformExtractor.extractTitleFromUrl(url)
        Log.d(TAG, "提取的标题: $title")

        // 统一使用webviewExtractHtml调用extractVideo来获取视频链接
        Log.d(TAG, "开始调用webviewExtractHtml执行extractVideo")
        extractVideoFromUrlForTask(context, url, platform, title, sourceType, taskTitle)
    }

    /**
     * 从URL提取视频资源用于创建任务
     * 使用webviewExtractHtml调用extractVideo获取视频链接，然后用最终的视频链接创建任务
     *
     * @param context 上下文
     * @param url 原始URL
     * @param platform 平台信息
     * @param title 标题
     * @param sourceType 内容源类型：1=音视频内容，2=图文内容
     * @param taskTitle 任务标题
     */
    private fun extractVideoFromUrlForTask(
        context: Context,
        url: String,
        platform: String,
        title: String,
        sourceType: Int,
        taskTitle: String
    ) {
        Log.d(TAG, "开始从URL提取视频资源用于创建任务: $url, 源类型: $sourceType")

        // 使用WebViewHtmlExtractor执行extractVideo.js脚本
        WebViewHtmlExtractor.executeBusinessJavaScript(
            context = context,
            url = url,
            businessName = "extractVideo"
        ) { result, error ->
            if (error != null) {
                Log.e(TAG, "视频提取失败 - URL: $url, 错误: $error")
                // 清空笔记标题和封面
                NoteApi.currentNoteTitle = ""
                NoteApi.currentNoteCover = ""
                Log.d(TAG, "视频提取失败，已清空笔记标题和封面")
                // 视频提取失败，提示用户失败，不创建任务
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    android.widget.Toast.makeText(
                        context,
                        "视频提取失败: $error",
                        android.widget.Toast.LENGTH_LONG
                    ).show()
                }
                return@executeBusinessJavaScript
            } else if (result != null) {
                Log.d(TAG, "视频提取成功，结果: $result")

                try {
                    // 解析JSON结果
                    val jsonResult = org.json.JSONObject(result)
                    val success = jsonResult.optBoolean("success", false)

                    if (success) {
                        // 提取封面信息 - 直接从coverImage对象中提取
                        var finalCoverImage = ""

                        val coverImageObj = jsonResult.optJSONObject("coverImage")
                        if (coverImageObj != null) {
                            val coverSrc = coverImageObj.optString("src", "")
                            if (coverSrc.isNotEmpty() && coverSrc != "null") {
                                finalCoverImage = coverSrc
                                Log.d(TAG, "从coverImage对象提取到封面: $finalCoverImage")
                            }
                        }

                        // 处理封面上传
                        if (finalCoverImage.isNotEmpty()) {
                            Log.d(TAG, "找到封面图片，开始上传到OSS: $finalCoverImage")
                            // 使用SourceToOssLinkHelper上传封面到OSS
                            SourceToOssLinkHelper.convertToOssLinkAsync(context, finalCoverImage, object : SourceToOssLinkHelper.ConvertCallback {
                                override fun onSuccess(ossUrl: String) {
                                    Log.d(TAG, "封面上传OSS成功，保存OSS链接到NoteApi: $ossUrl")
                                    NoteApi.currentNoteCover = ossUrl
                                }

                                override fun onFailure(error: Exception) {
                                    Log.e(TAG, "封面上传OSS失败: ${error.message}，使用原始链接")
                                    NoteApi.currentNoteCover = finalCoverImage // 如果上传失败，使用原始链接
                                }
                            })
                        } else {
                            Log.d(TAG, "未找到有效封面")
                            NoteApi.currentNoteCover = ""
                        }

                        val videosArray = jsonResult.optJSONArray("videos")
                        if (videosArray != null && videosArray.length() > 0) {
                            // 获取第一个视频的URL
                            val firstVideo = videosArray.getJSONObject(0)
                            val videoSrc = firstVideo.optString("src", "")
                            val videoCurrentSrc = firstVideo.optString("currentSrc", "")

                            Log.d(TAG, "解析视频信息 - src: $videoSrc, currentSrc: $videoCurrentSrc")

                            // 优先使用currentSrc，如果没有或为null则使用src
                            val videoUrl = when {
                                videoCurrentSrc.isNotEmpty() && videoCurrentSrc != "null" -> videoCurrentSrc
                                videoSrc.isNotEmpty() && videoSrc != "null" -> videoSrc
                                else -> ""
                            }

                            if (videoUrl.isNotEmpty()) {
                                Log.d(TAG, "使用提取的视频URL创建任务: $videoUrl")

                                downloadVideo(context,videoUrl) { downloadSuccess, filePath, errorMessage ->
                                    // 下载完成后的回调处理
                                    if (downloadSuccess && !filePath.isNullOrEmpty()) {
                                        Log.d(TAG, "视频下载完成: $filePath")

                                        // 下载成功后上传到OSS
                                        uploadVideoToOss(context,filePath) { ossSuccess, ossUrl, ossError ->
                                            if (ossSuccess && !ossUrl.isNullOrEmpty()) {
                                                Log.d(TAG, "视频上传OSS完成: $ossUrl")

                                                // 删除刚刚下载的视频文件
                                                try {
                                                    val file = java.io.File(filePath)
                                                    if (file.exists()) {
                                                        val deleted = file.delete()
                                                        if (deleted) {
                                                            Log.d(TAG, "成功删除临时视频文件: $filePath")
                                                        } else {
                                                            Log.w(TAG, "删除临时视频文件失败: $filePath")
                                                        }
                                                    } else {
                                                        Log.w(TAG, "临时视频文件不存在: $filePath")
                                                    }
                                                } catch (e: Exception) {
                                                    Log.e(TAG, "删除临时视频文件时发生异常: $filePath", e)
                                                }

                                                // 找到视频链接，创建笔记任务（使用视频URL）
                                                createTask(context,3,taskTitle,platform,ossUrl,sourceType)
                                            } else {
                                                Log.e(TAG, "视频上传OSS失败: $ossError")
                                                // 清空笔记标题和封面
                                                NoteApi.currentNoteTitle = ""
                                                NoteApi.currentNoteCover = ""
                                                Log.d(TAG, "视频上传OSS失败，已清空笔记标题和封面")
                                            }
                                        }
                                    } else {
                                        Log.e(TAG, "视频下载失败: $errorMessage")
                                        // 清空笔记标题和封面
                                        NoteApi.currentNoteTitle = ""
                                        NoteApi.currentNoteCover = ""
                                        Log.d(TAG, "视频下载失败，已清空笔记标题和封面")
                                        CustomToastHelper.showToast(context, "视频下载失败")
                                    }
                                }
                            } else {
                                // 清空笔记标题和封面
                                NoteApi.currentNoteTitle = ""
                                NoteApi.currentNoteCover = ""
                                Log.d(TAG, "未能提取视频链接，已清空笔记标题和封面")
                                CustomToastHelper.showToast(context, "未能成功提取视频链接，请稍后再试")
                            }
                        } else {
                            // 清空笔记标题和封面
                            NoteApi.currentNoteTitle = ""
                            NoteApi.currentNoteCover = ""
                            Log.d(TAG, "未找到视频数组，已清空笔记标题和封面")
                            CustomToastHelper.showToast(context, "未能成功提取视频链接，请稍后再试")
                        }
                    } else {
                        // 清空笔记标题和封面
                        NoteApi.currentNoteTitle = ""
                        NoteApi.currentNoteCover = ""
                        Log.d(TAG, "视频提取不成功，已清空笔记标题和封面")
                        CustomToastHelper.showToast(context, "未能成功提取视频链接，请稍后再试")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "解析视频提取结果时出错", e)
                    // 清空笔记标题和封面
                    NoteApi.currentNoteTitle = ""
                    NoteApi.currentNoteCover = ""
                    Log.d(TAG, "解析视频结果出错，已清空笔记标题和封面")
                    CustomToastHelper.showToast(context, "解析视频出错，请稍后再试")
                }
            } else {
                Log.d(TAG, "未从URL提取到任何视频资源，提示用户失败")
                // 清空笔记标题和封面
                NoteApi.currentNoteTitle = ""
                NoteApi.currentNoteCover = ""
                Log.d(TAG, "未提取到视频资源，已清空笔记标题和封面")
                CustomToastHelper.showToast(context, "剪切板内容解析异常，请稍后再试")
            }
        }
    }

    private fun downloadVideo(
        context: Context,
        videoUrl: String,
        onComplete: ((Boolean, String?, String?) -> Unit)? = null
    ) {
        // 调用FFmpegHelper中的downloadVideo下载视频到相册
        Log.d(TAG, "开始下载视频到相册: $videoUrl")
        val ffmpegHelper = FFmpegHelper.getInstance(context)
        val userAgent =
            com.xunhe.aishoucang.helpers.UserAgentGenerator.generateUserAgentForUrl(videoUrl)
        val referer = com.xunhe.aishoucang.helpers.UserAgentGenerator.getRefererForUrl(videoUrl)

        ffmpegHelper.downloadVideo(
            url = videoUrl,
            userAgent = userAgent,
            referer = referer,
            outputPath = null, // 使用默认路径
            saveToGallery = false // 保存到相册
        ) { success, outputFilePath, errorMessage ->
            android.os.Handler(android.os.Looper.getMainLooper()).post {
                // 执行回调（如果提供了的话）
                onComplete?.invoke(success, outputFilePath, errorMessage)
            }
        }
    }

    /**
     * 上传视频到OSS
     *
     * @param context 上下文
     * @param filePath 本地文件路径
     * @param onComplete 完成回调，参数为(是否成功, OSS URL或本地路径, 错误信息)
     */
    private fun uploadVideoToOss(
        context: Context,
        filePath: String,
        onComplete: ((Boolean, String?, String?) -> Unit)? = null
    ) {
        Log.d(TAG, "开始上传视频到OSS: $filePath")

        // 在后台线程执行OSS上传
        Thread {
            try {
                val ossManager = OssManager.getInstance(context)
                val ossUrl = ossManager.uploadFileSync(filePath)

                // 在主线程中执行回调
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    Log.d(TAG, "视频上传OSS成功: $ossUrl")
                    onComplete?.invoke(true, ossUrl, null)
                }
            } catch (e: Exception) {
                // 在主线程中执行回调
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    Log.e(TAG, "视频上传OSS失败: ${e.message}", e)
                    CustomToastHelper.showToast(context, "视频上传失败: ${e.message}")
                    onComplete?.invoke(false, filePath, e.message)
                }
            }
        }.start()
    }

    /**
     * 创建任务
     *
     * @param context 上下文
     * @param taskType 任务类型
     * @param title 标题
     * @param platform 平台
     * @param url URL
     * @param sourceType 内容源类型
     */
    private fun createTask(
        context: Context,
        taskType: Int,
        title: String,
        platform: String,
        url: String,
        sourceType: Int
    ) {
        Log.d(TAG, "开始创建任务")
        Log.d(TAG, "任务类型: $taskType, 标题: $title, 平台: $platform, URL: $url, 内容源类型: $sourceType")

        val taskApi = TaskApi.getInstance(context)
        taskApi.createTask(
            taskType = taskType,
            title = title,
            platform = platform,
            url = url,
            sourceType = sourceType
        ) { success, result, error ->
            // 在主线程中显示结果
            android.os.Handler(android.os.Looper.getMainLooper()).post {
                if (success) {
                    CustomToastHelper.showToast(context, "任务创建成功")
                    // 解析任务创建结果，获取任务ID
                    if (result != null) {
                        try {
                            val taskData = org.json.JSONObject(result)
                            val taskId = taskData.optString("id")

                            if (taskId.isNotEmpty()) {
                                Log.d(TAG, "任务创建成功，开始轮询任务状态 - 任务ID: $taskId")

                                // 开始轮询任务状态
                                val taskStatusPoller = TaskStatusPoller.getInstance()
                                taskStatusPoller.startPolling(
                                    context = context,
                                    taskId = taskId,
                                    taskType = taskType,
                                    taskTitle = title,
                                    platform = platform,
                                    url = url,
                                    sourceType = sourceType
                                )
                            } else {
                                Log.w(TAG, "任务创建成功但未获取到任务ID")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "解析任务创建结果失败", e)
                        }
                    }
                } else {
                    // 清空笔记标题和封面
                    NoteApi.currentNoteTitle = ""
                    NoteApi.currentNoteCover = ""
                    Log.d(TAG, "任务创建失败，已清空笔记标题和封面")
                    CustomToastHelper.showToast(context, "任务创建失败，请稍后再试")
                }
            }
        }
    }

    /**
     * 处理剪切板内容（保留原有方法，但不再使用）
     *
     * @param context 上下文
     */
    private fun processClipboardContent(context: Context) {
        try {
            // 1. 获取剪切板内容
            val clipboardHelper = ClipboardHelper.getInstance(context)
            val clipboardContent = clipboardHelper.getClipboardText()

            Log.d(TAG, "获取到剪切板内容: $clipboardContent")

            if (clipboardContent.isNullOrEmpty()) {
                Log.w(TAG, "剪切板内容为空")
                // 剪切板为空，隐藏进度
                TaskProgressHelper.hideProgress()
                return
            }

            // 2. 从剪切板内容中提取链接
            val extractedLinks = extractLinks(clipboardContent)

            // 3. 打印提取到的链接
            if (extractedLinks.isNotEmpty()) {
                Log.d(TAG, "成功提取到 ${extractedLinks.size} 个链接:")
                extractedLinks.forEachIndexed { index, link ->
                    Log.d(TAG, "链接 ${index + 1}: $link")
                }

                // 4. 对每个链接进行视频资源拦截，收集所有视频URL
                val allVideoUrls = mutableListOf<String>()
                var processedLinksCount = 0
                val totalLinksCount = extractedLinks.size

                extractedLinks.forEach { url ->
                    extractVideoFromUrl(context, url) { videoUrls ->
                        // 收集视频URL到统一列表中
                        if (videoUrls.isNotEmpty()) {
                            Log.d(TAG, "从链接 $url 提取到 ${videoUrls.size} 个视频URL:")
                            videoUrls.forEachIndexed { index, videoUrl ->
                                Log.d(TAG, "  视频URL ${index + 1}: $videoUrl")
                            }
                            allVideoUrls.addAll(videoUrls)
                        } else {
                            Log.d(TAG, "从链接 $url 未提取到视频URL")
                        }

                        // 检查是否所有链接都已处理完成
                        processedLinksCount++

                        // 更新URL解析进度（占"提取视频"步骤的50%）
                        val urlParseProgress =
                            (processedLinksCount.toFloat() / totalLinksCount) * 50f
                        TaskProgressHelper.updateCurrentProgress(urlParseProgress)

                        if (processedLinksCount >= totalLinksCount) {
                            // 所有链接处理完成，URL解析完成（50%），开始视频下载（剩余50%）
                            Log.d(TAG, "URL解析完成，开始视频下载")
                            processCollectedVideoUrls(context, allVideoUrls)
                        }
                    }
                }
            } else {
                Log.d(TAG, "未从剪切板内容中找到HTTP/HTTPS链接")
                // 没有找到链接，直接完成所有步骤
                TaskProgressHelper.nextStep() // 解析文案
                TaskProgressHelper.nextStep() // 解析完成
            }

        } catch (e: Exception) {
            Log.e(TAG, "处理剪切板内容时发生错误", e)
            // 发生异常，隐藏进度
            TaskProgressHelper.hideProgress()
        }
    }

    /**
     * 处理收集到的视频URL列表
     * 进行过滤、去重，然后批量下载
     *
     * @param context 上下文
     * @param videoUrls 收集到的视频URL列表
     */
    private fun processCollectedVideoUrls(context: Context, videoUrls: List<String>) {
        Log.d(TAG, "开始处理收集到的 ${videoUrls.size} 个视频URL")

        // 使用VideoUrlProcessor进行过滤、去重和下载
        VideoUrlProcessor.processAndDownloadVideos(
            context = context,
            videoUrls = videoUrls,
            onProgress = { step, progress ->
                // 根据步骤更新对应的进度
                when (step) {
                    "下载视频" -> {
                        // 视频下载占"提取视频"步骤的后50%（50%-100%）
                        if (TaskProgressHelper.getCurrentStep() == TaskProgressHelper.ProgressStep.EXTRACT_VIDEO) {
                            val adjustedProgress = 50f + (progress * 0.5f)
                            TaskProgressHelper.updateCurrentProgress(adjustedProgress)

                            // 如果视频下载完成（进度达到100%），立即切换到"解析文案"步骤
                            if (progress >= 100f) {
                                Log.d(TAG, "视频下载完成，立即切换到解析文案步骤")
                                TaskProgressHelper.nextStep() // 完成"提取视频"步骤，进入"解析文案"步骤
                            }
                        }
                    }
                    "转换音频" -> {
                        // 转换音频现在属于"解析文案"步骤
                        if (TaskProgressHelper.getCurrentStep() == TaskProgressHelper.ProgressStep.PARSE_CONTENT) {
                            // 转换音频占"解析文案"步骤的前30%（0%-30%）
                            val adjustedProgress = progress * 0.3f
                            TaskProgressHelper.updateCurrentProgress(adjustedProgress)
                        }
                    }
                    "语音识别" -> {
                        // 语音识别占"解析文案"步骤的后70%（30%-100%）
                        if (TaskProgressHelper.getCurrentStep() == TaskProgressHelper.ProgressStep.PARSE_CONTENT) {
                            val adjustedProgress = 30f + (progress * 0.7f)
                            TaskProgressHelper.updateCurrentProgress(adjustedProgress)

                            // 如果语音识别完成（进度达到100%），则进入最终步骤
                            if (progress >= 100f) {
                                Log.d(TAG, "语音识别完成，进入最终步骤")
                                TaskProgressHelper.nextStep() // 完成"解析文案"步骤，进入"解析完成"步骤
                            }
                        }
                    }
                }
            },
            onComplete = { success, processedCount, errorMessage ->
                if (success) {
                    Log.d(TAG, "视频处理完成，成功处理 $processedCount 个视频")
                    // 步骤切换已经在"转换音频"完成时处理了，这里不需要再调用nextStep
                    // 语音识别完成后会自动调用nextStep完成"解析文案"步骤
                    Log.d(TAG, "等待语音识别完成...")
                } else {
                    Log.e(TAG, "视频处理失败: $errorMessage")
                    // 发生异常，隐藏进度
                    TaskProgressHelper.hideProgress()
                }
            }
        )
    }

    /**
     * 从指定URL提取视频资源
     *
     * @param context 上下文
     * @param url 要检查的URL
     * @param callback 回调函数，参数为提取到的视频URL列表
     */
    private fun extractVideoFromUrl(
        context: Context,
        url: String,
        callback: ((List<String>) -> Unit)? = null
    ) {
        Log.d(TAG, "开始从URL提取视频资源: $url")

        // 使用WebViewHtmlExtractor执行extractVideo.js脚本
        WebViewHtmlExtractor.executeBusinessJavaScript(
            context = context,
            url = url,
            businessName = "extractVideo"
        ) { result, error ->
            if (error != null) {
                Log.e(TAG, "视频提取失败 - URL: $url, 错误: $error")
                callback?.invoke(emptyList())
            } else if (result != null) {
                Log.d(TAG, "=== 视频提取成功 ===")
                Log.d(TAG, "原始URL: $url")
                Log.d(TAG, "提取结果: $result")

                try {
                    // 解析JSON结果
                    val jsonResult = org.json.JSONObject(result)
                    val success = jsonResult.optBoolean("success", false)

                    if (success) {
                        val videoCount = jsonResult.optInt("videoCount", 0)
                        Log.d(TAG, "成功找到 $videoCount 个视频")

                        val videoUrls = mutableListOf<String>()
                        val videosArray = jsonResult.optJSONArray("videos")
                        if (videosArray != null) {
                            for (i in 0 until videosArray.length()) {
                                val video = videosArray.getJSONObject(i)
                                val src = video.optString("src")
                                val currentSrc = video.optString("currentSrc")
                                val poster = video.optString("poster")

                                Log.d(TAG, "视频 ${i + 1}:")
                                if (src.isNotEmpty()) {
                                    Log.d(TAG, "  src: $src")
                                    videoUrls.add(src)
                                }
                                if (currentSrc.isNotEmpty()) {
                                    Log.d(TAG, "  currentSrc: $currentSrc")
                                    // 如果currentSrc与src不同，也添加到列表中
                                    if (currentSrc != src) {
                                        videoUrls.add(currentSrc)
                                    }
                                }
                                if (poster.isNotEmpty()) Log.d(TAG, "  poster: $poster")
                            }
                        }

                        // 调用回调函数，传递提取到的视频URL列表
                        if (videoUrls.isNotEmpty()) {
                            Log.d(TAG, "调用回调函数，传递 ${videoUrls.size} 个视频URL")
                            callback?.invoke(videoUrls)
                        } else {
                            Log.d(TAG, "未找到有效的视频URL")
                            callback?.invoke(emptyList())
                        }
                    } else {
                        val errorMsg = jsonResult.optString("error", "未知错误")
                        Log.d(TAG, "视频提取失败: $errorMsg")
                        callback?.invoke(emptyList())
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "解析视频提取结果时出错", e)
                    callback?.invoke(emptyList())
                }

                Log.d(TAG, "=== 视频提取结果结束 ===")
            } else {
                Log.d(TAG, "未从URL提取到任何视频资源: $url")
                callback?.invoke(emptyList())
            }
        }
    }


    /**
     * 从文本中提取HTTP/HTTPS链接（公共方法）
     *
     * @param text 要提取链接的文本
     * @return 提取到的链接列表
     */
    fun extractLinksFromText(text: String): List<String> {
        return extractLinks(text)
    }

    /**
     * 从文本中提取HTTP/HTTPS链接
     *
     * @param text 要提取链接的文本
     * @return 提取到的链接列表
     */
    private fun extractLinks(text: String): List<String> {
        val links = mutableListOf<String>()

        try {
            val matcher = URL_PATTERN.matcher(text)

            while (matcher.find()) {
                val link = matcher.group()
                if (!links.contains(link)) {
                    links.add(link)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "提取链接时发生错误", e)
        }

        return links
    }

    /**
     * 检查URL是否为支持的平台（抖音或小红书）
     *
     * @param url 要检查的URL
     * @return true如果是支持的平台，否则返回false
     */
    private fun isSupportedPlatform(url: String): Boolean {
        if (url.isEmpty()) {
            return false
        }

        try {
            val uri = java.net.URL(url)
            val host = uri.host?.lowercase() ?: ""

            Log.d(TAG, "检查平台支持: URL=$url, Host=$host")

            return when {
                // 抖音
                host.contains("douyin.com") || host.contains("iesdouyin.com") -> {
                    Log.d(TAG, "检测到抖音链接")
                    true
                }
                // 小红书
                host.contains("xiaohongshu.com") || host.contains("xhslink.com") -> {
                    Log.d(TAG, "检测到小红书链接")
                    true
                }
                else -> {
                    Log.d(TAG, "不支持的平台: $host")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查平台支持时发生错误: $url", e)
            return false
        }
    }

    /**
     * 从WebView结果中提取拦截的scheme URL
     */
    private fun extractInterceptedSchemeUrl(result: String): String? {
        return try {
            val lines = result.split("\n")
            for (line in lines) {
                if (line.startsWith("InterceptedSchemeUrl: ")) {
                    return line.substring("InterceptedSchemeUrl: ".length)
                }
            }
            null
        } catch (e: Exception) {
            Log.e(TAG, "提取scheme URL失败", e)
            null
        }
    }

    /**
     * 从小红书scheme URL中提取封面图片
     */
    private fun extractCoverFromSchemeUrl(schemeUrl: String): String? {
        return try {
            Log.d(TAG, "尝试从scheme URL提取封面: $schemeUrl")

            // 解码URL
            val decodedUrl = java.net.URLDecoder.decode(schemeUrl, "UTF-8")
            Log.d(TAG, "解码后的scheme URL: $decodedUrl")

            // 查找h5VideoPreloadInfo参数
            val h5VideoPreloadInfoRegex = "h5VideoPreloadInfo=([^&]+)".toRegex()
            val matchResult = h5VideoPreloadInfoRegex.find(decodedUrl)

            if (matchResult != null) {
                val preloadInfoEncoded = matchResult.groupValues[1]
                val preloadInfoDecoded = java.net.URLDecoder.decode(preloadInfoEncoded, "UTF-8")
                Log.d(TAG, "提取到预加载信息: $preloadInfoDecoded")

                // 解析JSON
                val preloadInfo = org.json.JSONObject(preloadInfoDecoded)
                val videoInfoV2 = preloadInfo.optJSONObject("video_info_v2")

                if (videoInfoV2 != null) {
                    val image = videoInfoV2.optJSONObject("image")
                    if (image != null) {
                        val firstFrame = image.optString("first_frame", "")
                        if (firstFrame.isNotEmpty()) {
                            Log.d(TAG, "从scheme URL成功提取封面: $firstFrame")
                            return firstFrame
                        }
                    }
                }
            }

            Log.d(TAG, "未能从scheme URL提取到封面")
            null
        } catch (e: Exception) {
            Log.e(TAG, "从scheme URL提取封面失败", e)
            null
        }
    }
}
